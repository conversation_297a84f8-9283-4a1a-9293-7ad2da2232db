package dev.pigmomo.yhkit2025

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import dev.pigmomo.yhkit2025.ui.screens.MonitoringDataScreen
import dev.pigmomo.yhkit2025.ui.theme.Yhkit2025Theme

/**
 * 监控数据展示Activity
 * 用于展示所有监控商品的详细数据和变化记录
 */
class MonitoringDataActivity : ComponentActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        setContent {
            Yhkit2025Theme(dynamicColor = false) {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    MonitoringDataScreen(
                        onNavigateBack = {
                            finish()
                        }
                    )
                }
            }
        }
    }
}
