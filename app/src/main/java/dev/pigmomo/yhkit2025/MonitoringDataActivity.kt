package dev.pigmomo.yhkit2025

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import dev.pigmomo.yhkit2025.ui.screens.MonitoringDataScreen
import dev.pigmomo.yhkit2025.ui.theme.Yhkit2025Theme

/**
 * 监控数据展示Activity
 * 重新设计：以商品ID和sellerId为筛选项，显示商品信息和变化图表
 */
class MonitoringDataActivity : ComponentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        setContent {
            Yhkit2025Theme(dynamicColor = false) {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    MonitoringDataScreen(
                        onNavigateBack = {
                            finish()
                        }
                    )
                }
            }
        }
    }
}
