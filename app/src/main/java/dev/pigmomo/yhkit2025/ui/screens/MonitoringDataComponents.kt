package dev.pigmomo.yhkit2025.ui.screens

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeRecordEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeType
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import java.text.SimpleDateFormat
import java.util.*

/**
 * 筛选对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FilterDialog(
    productIdFilter: String,
    sellerIdFilter: String,
    availableSellerIds: List<String>,
    onProductIdChange: (String) -> Unit,
    onSellerIdChange: (String) -> Unit,
    onDismiss: () -> Unit,
    onClearAll: () -> Unit
) {
    var tempProductId by remember { mutableStateOf(productIdFilter) }
    var tempSellerId by remember { mutableStateOf(sellerIdFilter) }
    var showSellerDropdown by remember { mutableStateOf(false) }

    Dialog(onDismissRequest = onDismiss) {
        Card(
            colors = cardThemeOverlay(),
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "筛选条件",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold
                )

                // 商品ID筛选
                OutlinedTextField(
                    value = tempProductId,
                    onValueChange = { tempProductId = it },
                    label = { Text("商品ID") },
                    placeholder = { Text("输入商品ID进行筛选") },
                    modifier = Modifier.fillMaxWidth(),
                    trailingIcon = {
                        if (tempProductId.isNotEmpty()) {
                            IconButton(onClick = { tempProductId = "" }) {
                                Icon(Icons.Default.Clear, contentDescription = "清除")
                            }
                        }
                    }
                )

                // 卖家ID筛选
                ExposedDropdownMenuBox(
                    expanded = showSellerDropdown,
                    onExpandedChange = { showSellerDropdown = it }
                ) {
                    OutlinedTextField(
                        value = tempSellerId,
                        onValueChange = { },
                        readOnly = true,
                        label = { Text("卖家ID") },
                        placeholder = { Text("选择卖家ID") },
                        modifier = Modifier
                            .fillMaxWidth()
                            .menuAnchor(),
                        trailingIcon = {
                            Row {
                                if (tempSellerId.isNotEmpty()) {
                                    IconButton(onClick = { tempSellerId = "" }) {
                                        Icon(Icons.Default.Clear, contentDescription = "清除")
                                    }
                                }
                                ExposedDropdownMenuDefaults.TrailingIcon(expanded = showSellerDropdown)
                            }
                        }
                    )

                    ExposedDropdownMenu(
                        expanded = showSellerDropdown,
                        onDismissRequest = { showSellerDropdown = false }
                    ) {
                        availableSellerIds.forEach { sellerId ->
                            DropdownMenuItem(
                                text = { Text(sellerId) },
                                onClick = {
                                    tempSellerId = sellerId
                                    showSellerDropdown = false
                                }
                            )
                        }
                    }
                }

                // 按钮行
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    TextButton(
                        onClick = {
                            onClearAll()
                            tempProductId = ""
                            tempSellerId = ""
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("清除全部")
                    }

                    Button(
                        onClick = {
                            onProductIdChange(tempProductId)
                            onSellerIdChange(tempSellerId)
                            onDismiss()
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("应用")
                    }
                }
            }
        }
    }
}

/**
 * 商品列表标签页
 */
@Composable
fun ProductListTab(
    products: List<ProductMonitorEntity>,
    onProductClick: (ProductMonitorEntity) -> Unit
) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        item {
            Text(
                text = "监控商品 (${products.size})",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(bottom = 8.dp)
            )
        }

        items(products) { product ->
            ProductListItem(
                product = product,
                onClick = { onProductClick(product) }
            )
        }
    }
}

/**
 * 商品列表项
 */
@Composable
fun ProductListItem(
    product: ProductMonitorEntity,
    onClick: () -> Unit
) {
    val dateFormat = remember { SimpleDateFormat("MM-dd HH:mm", Locale.getDefault()) }

    Card(
        colors = cardThemeOverlay(),
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            // 商品标题
            Text(
                text = product.title,
                fontWeight = FontWeight.Medium,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )

            if (product.subtitle.isNotEmpty()) {
                Text(
                    text = product.subtitle,
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 商品信息行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "ID: ${product.id}",
                        fontSize = 11.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "卖家: ${product.sellerId}",
                        fontSize = 11.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                Column(
                    horizontalAlignment = Alignment.End
                ) {
                    // 价格信息
                    if (product.currentPrice > 0) {
                        Text(
                            text = "¥${String.format("%.2f", product.currentPrice / 100.0)}",
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }

                    // 库存状态
                    Surface(
                        color = when {
                            product.available == 1 -> Color(0xFF4CAF50)
                            product.isSeckill == 1 -> Color(0xFFFF9800)
                            else -> Color(0xFFF44336)
                        },
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Text(
                            text = when {
                                product.available == 1 -> "有货"
                                product.isSeckill == 1 -> "秒杀"
                                else -> "缺货"
                            },
                            color = Color.White,
                            fontSize = 10.sp,
                            modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(4.dp))

            // 更新时间
            Text(
                text = "更新: ${dateFormat.format(product.lastUpdateTime)}",
                fontSize = 11.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 商品详情标签页
 */
@Composable
fun ProductDetailTab(
    product: ProductMonitorEntity?,
    onBackToList: () -> Unit
) {
    if (product == null) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "请选择一个商品查看详情",
                    fontSize = 16.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Button(onClick = onBackToList) {
                    Text("返回商品列表")
                }
            }
        }
        return
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 返回按钮
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBackToList) {
                Icon(Icons.Default.ArrowBack, contentDescription = "返回")
            }
            Text(
                text = "商品详情",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold
            )
        }

        // 商品基本信息
        ProductDetailCard(product)
    }
}

/**
 * 商品详情卡片
 */
@Composable
fun ProductDetailCard(product: ProductMonitorEntity) {
    val dateFormat = remember { SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()) }

    Card(
        colors = cardThemeOverlay(),
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "基本信息",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold
            )

            DetailRow("商品ID", product.id)
            DetailRow("商品标题", product.title)
            if (product.subtitle.isNotEmpty()) {
                DetailRow("副标题", product.subtitle)
            }
            if (product.originalSkuCode.isNotEmpty()) {
                DetailRow("SKU码", product.originalSkuCode)
            }
            DetailRow("卖家ID", product.sellerId)
            if (product.categoryId.isNotEmpty()) {
                DetailRow("分类ID", product.categoryId)
            }

            Divider()

            Text(
                text = "价格信息",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold
            )

            DetailRow("当前价格", "¥${String.format("%.2f", product.currentPrice / 100.0)}")
            if (product.marketPrice > 0) {
                DetailRow("市场价格", "¥${String.format("%.2f", product.marketPrice / 100.0)}")
            }
            if (product.priceKind.isNotEmpty()) {
                DetailRow("价格类型", product.priceKind)
            }

            Divider()

            Text(
                text = "库存信息",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold
            )

            DetailRow("库存数量", product.stockNum.toString())
            DetailRow("可用状态", if (product.available == 1) "有货" else "缺货")
            DetailRow("秒杀商品", if (product.isSeckill == 1) "是" else "否")
            DetailRow("可购买", if (!product.canNotBuy) "是" else "否")

            if (product.restrictLimit > 0) {
                DetailRow("限购数量", product.restrictLimit.toString())
            }

            Divider()

            Text(
                text = "监控信息",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold
            )

            DetailRow("监控状态", if (product.isMonitoringEnabled) "启用" else "禁用")
            DetailRow("首次添加", dateFormat.format(product.firstAddTime))
            DetailRow("最后更新", dateFormat.format(product.lastUpdateTime))
            if (product.monitorNote.isNotEmpty()) {
                DetailRow("监控备注", product.monitorNote)
            }
        }
    }
}

/**
 * 详情行组件
 */
@Composable
fun DetailRow(label: String, value: String) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            fontSize = 14.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.weight(1f)
        )
        Text(
            text = value,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.weight(2f)
        )
    }
}

/**
 * 变化记录标签页
 */
@Composable
fun ChangeRecordsTab(
    changeRecords: List<ProductChangeRecordEntity>,
    dateFormat: SimpleDateFormat
) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        item {
            Text(
                text = "变化记录 (${changeRecords.size})",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(bottom = 8.dp)
            )
        }

        items(changeRecords) { record ->
            ChangeRecordCard(record, dateFormat)
        }
    }
}

/**
 * 变化记录卡片
 */
@Composable
fun ChangeRecordCard(
    record: ProductChangeRecordEntity,
    dateFormat: SimpleDateFormat
) {
    Card(
        colors = cardThemeOverlay(),
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = record.productId,
                    fontWeight = FontWeight.Medium,
                    fontSize = 12.sp,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )

                Surface(
                    color = when (record.changeType) {
                        ProductChangeType.PRICE_CHANGE -> Color(0xFF2196F3)
                        ProductChangeType.STOCK_CHANGE -> Color(0xFF4CAF50)
                        ProductChangeType.AVAILABILITY_CHANGE -> Color(0xFFFF9800)
                        ProductChangeType.INFO_CHANGE -> Color(0xFF9C27B0)
                        ProductChangeType.SECKILL_STATUS_CHANGE -> Color(0xFFF44336)
                        else -> Color(0xFF607D8B)
                    },
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text(
                        text = when (record.changeType) {
                            ProductChangeType.PRICE_CHANGE -> "价格"
                            ProductChangeType.STOCK_CHANGE -> "库存"
                            ProductChangeType.AVAILABILITY_CHANGE -> "可用性"
                            ProductChangeType.INFO_CHANGE -> "信息"
                            ProductChangeType.SECKILL_STATUS_CHANGE -> "秒杀"
                            else -> "其他"
                        },
                        color = Color.White,
                        fontSize = 10.sp,
                        modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = "字段: ${record.fieldName}",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            if (record.oldValue.isNotEmpty() || record.newValue.isNotEmpty()) {
                Text(
                    text = "${record.oldValue} → ${record.newValue}",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.primary
                )
            }

            Text(
                text = dateFormat.format(record.changeTime),
                fontSize = 11.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 数据图表标签页
 */
@Composable
fun DataChartsTab(
    product: ProductMonitorEntity?,
    priceChartData: List<Pair<Long, Float>>,
    stockChartData: List<Pair<Long, Int>>
) {
    if (product == null) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "请选择一个商品查看图表",
                fontSize = 16.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        return
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "${product.title} - 数据图表",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold
        )

        // 价格趋势图
        if (priceChartData.isNotEmpty()) {
            ChartCard(
                title = "价格趋势",
                data = priceChartData.map { "${it.second}" },
                description = "显示商品价格随时间的变化趋势"
            )
        }

        // 库存变化图
        if (stockChartData.isNotEmpty()) {
            ChartCard(
                title = "库存变化",
                data = stockChartData.map { "${it.second}" },
                description = "显示商品库存随时间的变化情况"
            )
        }

        if (priceChartData.isEmpty() && stockChartData.isEmpty()) {
            Card(
                colors = cardThemeOverlay(),
                modifier = Modifier.fillMaxWidth()
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(32.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "暂无图表数据",
                        fontSize = 16.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

/**
 * 图表卡片（简化版本，实际项目中可以使用专业的图表库）
 */
@Composable
fun ChartCard(
    title: String,
    data: List<String>,
    description: String
) {
    Card(
        colors = cardThemeOverlay(),
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold
            )

            Text(
                text = description,
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(vertical = 8.dp)
            )

            // 简化的数据显示
            Text(
                text = "数据点: ${data.size}",
                fontSize = 14.sp
            )

            if (data.isNotEmpty()) {
                Text(
                    text = "最新值: ${data.last()}",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.primary
                )
            }

            // 这里可以集成真正的图表库，如MPAndroidChart或Compose Charts
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp)
                    .padding(top = 16.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "图表区域\n(可集成专业图表库)",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}
