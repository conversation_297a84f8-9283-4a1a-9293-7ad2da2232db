package dev.pigmomo.yhkit2025.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.FilterList
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewmodel.compose.viewModel
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeRecordEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeType
import dev.pigmomo.yhkit2025.service.productmonitor.MonitoringServiceManager
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.viewmodel.MonitoringDataViewModel
import java.text.SimpleDateFormat
import java.util.*

/**
 * 监控数据展示Screen
 * 重新设计：以商品ID和sellerId为筛选项，显示商品信息和变化图表
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MonitoringDataScreen(
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current

    // 获取ViewModel
    val productMonitorRepository = remember { MonitoringServiceManager.getProductMonitorRepository(context) }
    val viewModel: MonitoringDataViewModel = viewModel(
        factory = MonitoringDataViewModel.Factory(productMonitorRepository, context.applicationContext as android.app.Application)
    )

    // 收集状态
    val filteredProducts by viewModel.filteredProducts.collectAsState()
    val filteredChangeRecords by viewModel.filteredChangeRecords.collectAsState()
    val isLoading by viewModel.isLoading
    val selectedProduct by viewModel.selectedProduct
    val productIdFilter by viewModel.productIdFilter.collectAsState()
    val sellerIdFilter by viewModel.sellerIdFilter.collectAsState()
    val availableSellerIds by viewModel.availableSellerIds.collectAsState()
    val priceChartData by viewModel.priceChartData.collectAsState()
    val stockChartData by viewModel.stockChartData.collectAsState()

    // UI状态
    var showFilterDialog by remember { mutableStateOf(false) }
    var selectedTabIndex by remember { mutableIntStateOf(0) }

    val dateFormat = remember { SimpleDateFormat("MM-dd HH:mm", Locale.getDefault()) }
    val fullDateFormat = remember { SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()) }

    // 标签页选项
    val tabs = listOf("商品列表", "商品详情", "变化记录", "数据图表")
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("监控数据") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    IconButton(onClick = { showFilterDialog = true }) {
                        Icon(Icons.Default.FilterList, contentDescription = "筛选")
                    }
                    IconButton(onClick = { viewModel.refreshData() }) {
                        Icon(Icons.Default.Refresh, contentDescription = "刷新")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = Color.White,
                    navigationIconContentColor = Color.White,
                    actionIconContentColor = Color.White
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 筛选条件显示
            if (productIdFilter.isNotEmpty() || sellerIdFilter.isNotEmpty()) {
                FilterChipsRow(
                    productIdFilter = productIdFilter,
                    sellerIdFilter = sellerIdFilter,
                    onClearProductId = { viewModel.setProductIdFilter("") },
                    onClearSellerId = { viewModel.setSellerIdFilter("") },
                    onClearAll = { viewModel.clearFilters() }
                )
            }

            // 标签页
            TabRow(
                selectedTabIndex = selectedTabIndex,
                modifier = Modifier.fillMaxWidth()
            ) {
                tabs.forEachIndexed { index, title ->
                    Tab(
                        selected = selectedTabIndex == index,
                        onClick = { selectedTabIndex = index },
                        text = { Text(title) }
                    )
                }
            }

            // 内容区域
            if (isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else {
                when (selectedTabIndex) {
                    0 -> ProductListTab(
                        products = filteredProducts,
                        onProductClick = {
                            viewModel.selectProduct(it)
                            selectedTabIndex = 1
                        }
                    )
                    1 -> ProductDetailTab(
                        product = selectedProduct,
                        onBackToList = { selectedTabIndex = 0 }
                    )
                    2 -> ChangeRecordsTab(
                        changeRecords = filteredChangeRecords,
                        dateFormat = fullDateFormat
                    )
                    3 -> DataChartsTab(
                        product = selectedProduct,
                        priceChartData = priceChartData,
                        stockChartData = stockChartData
                    )
                }
            }
        }
    }

    // 筛选对话框
    if (showFilterDialog) {
        FilterDialog(
            productIdFilter = productIdFilter,
            sellerIdFilter = sellerIdFilter,
            availableSellerIds = availableSellerIds,
            onProductIdChange = { viewModel.setProductIdFilter(it) },
            onSellerIdChange = { viewModel.setSellerIdFilter(it) },
            onDismiss = { showFilterDialog = false },
            onClearAll = { viewModel.clearFilters() }
        )
    }
}
/**
 * 筛选条件显示行
 */
@Composable
fun FilterChipsRow(
    productIdFilter: String,
    sellerIdFilter: String,
    onClearProductId: () -> Unit,
    onClearSellerId: () -> Unit,
    onClearAll: () -> Unit
) {
    Card(
        colors = cardThemeOverlay(),
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "筛选:",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )

            if (productIdFilter.isNotEmpty()) {
                FilterChip(
                    onClick = onClearProductId,
                    label = { Text("商品ID: $productIdFilter") },
                    selected = true,
                    trailingIcon = {
                        Icon(
                            Icons.Default.Clear,
                            contentDescription = "清除",
                            modifier = Modifier.size(16.dp)
                        )
                    }
                )
            }

            if (sellerIdFilter.isNotEmpty()) {
                FilterChip(
                    onClick = onClearSellerId,
                    label = { Text("卖家ID: $sellerIdFilter") },
                    selected = true,
                    trailingIcon = {
                        Icon(
                            Icons.Default.Clear,
                            contentDescription = "清除",
                            modifier = Modifier.size(16.dp)
                        )
                    }
                )
            }

            Spacer(modifier = Modifier.weight(1f))

            TextButton(onClick = onClearAll) {
                Text("清除全部")
            }
        }
    }
}



