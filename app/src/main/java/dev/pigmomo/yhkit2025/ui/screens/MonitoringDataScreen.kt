package dev.pigmomo.yhkit2025.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringPlanEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeRecordEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeType
import dev.pigmomo.yhkit2025.data.repository.productmonitor.MonitoringPlanRepository
import dev.pigmomo.yhkit2025.data.repository.productmonitor.ProductMonitorRepository
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.components.TokenActionButton
import kotlinx.coroutines.launch
import org.koin.compose.koinInject
import java.text.SimpleDateFormat
import java.util.*

/**
 * 监控数据展示Screen
 * 用于展示所有监控商品的详细数据和变化记录
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MonitoringDataScreen(
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    
    // 注入Repository
    val monitoringPlanRepository: MonitoringPlanRepository = koinInject()
    val productMonitorRepository: ProductMonitorRepository = koinInject()
    
    // 状态管理
    var monitoringPlans by remember { mutableStateOf<List<MonitoringPlanEntity>>(emptyList()) }
    var monitoredProducts by remember { mutableStateOf<List<ProductMonitorEntity>>(emptyList()) }
    var changeRecords by remember { mutableStateOf<List<ProductChangeRecordEntity>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }
    var selectedTabIndex by remember { mutableIntStateOf(0) }
    var statistics by remember { mutableStateOf<Map<String, Int>>(emptyMap()) }
    
    val dateFormat = remember { SimpleDateFormat("MM-dd HH:mm", Locale.getDefault()) }
    val fullDateFormat = remember { SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()) }
    
    // 标签页选项
    val tabs = listOf("监控概览", "商品数据", "变化记录", "统计信息")
    
    // 加载数据
    fun loadData() {
        scope.launch {
            try {
                isLoading = true
                
                // 加载监控计划
                monitoringPlanRepository.getAllMonitoringPlans().collect { plans ->
                    monitoringPlans = plans
                }
                
                // 加载监控商品
                productMonitorRepository.getAllProducts().collect { products ->
                    monitoredProducts = products
                }
                
                // 加载变化记录
                productMonitorRepository.getAllChangeRecords().collect { records ->
                    changeRecords = records.sortedByDescending { it.changeTime }
                }
                
                // 加载统计信息
                statistics = productMonitorRepository.getMonitoringStatistics()
                
                isLoading = false
            } catch (e: Exception) {
                isLoading = false
            }
        }
    }
    
    // 初始化加载数据
    LaunchedEffect(Unit) {
        loadData()
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("监控数据") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    IconButton(onClick = { loadData() }) {
                        Icon(Icons.Default.Refresh, contentDescription = "刷新")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = Color.White,
                    navigationIconContentColor = Color.White,
                    actionIconContentColor = Color.White
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 标签页
            TabRow(
                selectedTabIndex = selectedTabIndex,
                modifier = Modifier.fillMaxWidth()
            ) {
                tabs.forEachIndexed { index, title ->
                    Tab(
                        selected = selectedTabIndex == index,
                        onClick = { selectedTabIndex = index },
                        text = { Text(title) }
                    )
                }
            }
            
            // 内容区域
            if (isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else {
                when (selectedTabIndex) {
                    0 -> MonitoringOverviewTab(monitoringPlans, statistics)
                    1 -> ProductDataTab(monitoredProducts)
                    2 -> ChangeRecordsTab(changeRecords, dateFormat, fullDateFormat)
                    3 -> StatisticsTab(statistics, monitoringPlans, monitoredProducts, changeRecords)
                }
            }
        }
    }
}

/**
 * 监控概览标签页
 */
@Composable
fun MonitoringOverviewTab(
    monitoringPlans: List<MonitoringPlanEntity>,
    statistics: Map<String, Int>
) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 概览卡片
        item {
            Card(
                colors = cardThemeOverlay(),
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "监控概览",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(bottom = 12.dp)
                    )
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        OverviewItem("监控计划", monitoringPlans.size.toString())
                        OverviewItem("启用计划", monitoringPlans.count { it.isEnabled }.toString())
                        OverviewItem("监控商品", statistics["total_products"]?.toString() ?: "0")
                        OverviewItem("可用商品", statistics["available_products"]?.toString() ?: "0")
                    }
                }
            }
        }
        
        // 监控计划列表
        item {
            Text(
                text = "监控计划列表",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(vertical = 8.dp)
            )
        }
        
        items(monitoringPlans) { plan ->
            MonitoringPlanOverviewItem(plan)
        }
    }
}

/**
 * 概览项组件
 */
@Composable
fun OverviewItem(label: String, value: String) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        Text(
            text = label,
            fontSize = 12.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 监控计划概览项
 */
@Composable
fun MonitoringPlanOverviewItem(plan: MonitoringPlanEntity) {
    val dateFormat = remember { SimpleDateFormat("MM-dd HH:mm", Locale.getDefault()) }
    
    Card(
        colors = cardThemeOverlay(),
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = plan.name,
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )
                
                Surface(
                    color = if (plan.isEnabled) Color(0xFF4CAF50) else Color(0xFF9E9E9E),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Text(
                        text = if (plan.isEnabled) "启用" else "禁用",
                        color = Color.White,
                        fontSize = 10.sp,
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 2.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = "账号: ${plan.account.nickname}",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Text(
                text = "商品数: ${plan.productIds.size}",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Text(
                text = "执行次数: ${plan.executedCount}",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            plan.lastExecutedAt?.let { lastExecuted ->
                Text(
                    text = "最后执行: ${dateFormat.format(lastExecuted)}",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 商品数据标签页
 */
@Composable
fun ProductDataTab(monitoredProducts: List<ProductMonitorEntity>) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        item {
            Text(
                text = "监控商品数据 (${monitoredProducts.size})",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(bottom = 8.dp)
            )
        }

        items(monitoredProducts) { product ->
            ProductDataItem(product)
        }
    }
}

/**
 * 商品数据项
 */
@Composable
fun ProductDataItem(product: ProductMonitorEntity) {
    val dateFormat = remember { SimpleDateFormat("MM-dd HH:mm", Locale.getDefault()) }

    Card(
        colors = cardThemeOverlay(),
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            // 商品标题
            Text(
                text = product.title,
                fontWeight = FontWeight.Medium,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )

            if (product.subtitle.isNotEmpty()) {
                Text(
                    text = product.subtitle,
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 商品信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "ID: ${product.id}",
                        fontSize = 11.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    if (product.originalSkuCode.isNotEmpty()) {
                        Text(
                            text = "SKU: ${product.originalSkuCode}",
                            fontSize = 11.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                Column(
                    horizontalAlignment = Alignment.End
                ) {
                    // 价格信息
                    if (product.currentPrice > 0) {
                        Text(
                            text = "¥${String.format("%.2f", product.currentPrice / 100.0)}",
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }

                    // 库存状态
                    Surface(
                        color = when {
                            product.isAvailable -> Color(0xFF4CAF50)
                            product.isSeckill -> Color(0xFFFF9800)
                            else -> Color(0xFFF44336)
                        },
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Text(
                            text = when {
                                product.isAvailable -> "有货"
                                product.isSeckill -> "秒杀"
                                else -> "缺货"
                            },
                            color = Color.White,
                            fontSize = 10.sp,
                            modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(4.dp))

            // 更新时间
            Text(
                text = "更新时间: ${dateFormat.format(product.lastUpdated)}",
                fontSize = 11.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 变化记录标签页
 */
@Composable
fun ChangeRecordsTab(
    changeRecords: List<ProductChangeRecordEntity>,
    dateFormat: SimpleDateFormat,
    fullDateFormat: SimpleDateFormat
) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        item {
            Text(
                text = "变化记录 (${changeRecords.size})",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(bottom = 8.dp)
            )
        }

        items(changeRecords) { record ->
            ChangeRecordItem(record, fullDateFormat)
        }
    }
}

/**
 * 变化记录项
 */
@Composable
fun ChangeRecordItem(
    record: ProductChangeRecordEntity,
    dateFormat: SimpleDateFormat
) {
    Card(
        colors = cardThemeOverlay(),
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = record.productId,
                    fontWeight = FontWeight.Medium,
                    fontSize = 12.sp,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )

                Surface(
                    color = when (record.changeType) {
                        ProductChangeType.PRICE_CHANGE -> Color(0xFF2196F3)
                        ProductChangeType.STOCK_CHANGE -> Color(0xFF4CAF50)
                        ProductChangeType.AVAILABILITY_CHANGE -> Color(0xFFFF9800)
                        ProductChangeType.TITLE_CHANGE -> Color(0xFF9C27B0)
                        ProductChangeType.SECKILL_STATUS_CHANGE -> Color(0xFFF44336)
                        else -> Color(0xFF607D8B)
                    },
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text(
                        text = when (record.changeType) {
                            ProductChangeType.PRICE_CHANGE -> "价格"
                            ProductChangeType.STOCK_CHANGE -> "库存"
                            ProductChangeType.AVAILABILITY_CHANGE -> "可用性"
                            ProductChangeType.TITLE_CHANGE -> "标题"
                            ProductChangeType.SECKILL_STATUS_CHANGE -> "秒杀"
                            else -> "其他"
                        },
                        color = Color.White,
                        fontSize = 10.sp,
                        modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = "字段: ${record.fieldName}",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            if (record.oldValue.isNotEmpty() || record.newValue.isNotEmpty()) {
                Text(
                    text = "${record.oldValue} → ${record.newValue}",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.primary
                )
            }

            Text(
                text = dateFormat.format(record.changeTime),
                fontSize = 11.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 统计信息标签页
 */
@Composable
fun StatisticsTab(
    statistics: Map<String, Int>,
    monitoringPlans: List<MonitoringPlanEntity>,
    monitoredProducts: List<ProductMonitorEntity>,
    changeRecords: List<ProductChangeRecordEntity>
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 监控计划统计
        Card(
            colors = cardThemeOverlay(),
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "监控计划统计",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(bottom = 12.dp)
                )

                StatisticRow("总计划数", monitoringPlans.size.toString())
                StatisticRow("启用计划", monitoringPlans.count { it.isEnabled }.toString())
                StatisticRow("禁用计划", monitoringPlans.count { !it.isEnabled }.toString())
                StatisticRow("间隔监控", monitoringPlans.count { it.operationType.name == "INTERVAL" }.toString())
                StatisticRow("定时监控", monitoringPlans.count { it.operationType.name == "SCHEDULED" }.toString())
                StatisticRow("手动监控", monitoringPlans.count { it.operationType.name == "MANUAL" }.toString())
            }
        }

        // 商品统计
        Card(
            colors = cardThemeOverlay(),
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "商品统计",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(bottom = 12.dp)
                )

                StatisticRow("总商品数", monitoredProducts.size.toString())
                StatisticRow("有货商品", monitoredProducts.count { it.isAvailable }.toString())
                StatisticRow("缺货商品", monitoredProducts.count { !it.isAvailable }.toString())
                StatisticRow("秒杀商品", monitoredProducts.count { it.isSeckill }.toString())
                StatisticRow("启用监控", monitoredProducts.count { it.isMonitoringEnabled }.toString())
            }
        }

        // 变化记录统计
        Card(
            colors = cardThemeOverlay(),
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "变化记录统计",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(bottom = 12.dp)
                )

                StatisticRow("总变化记录", changeRecords.size.toString())
                StatisticRow("价格变化", changeRecords.count { it.changeType == ProductChangeType.PRICE_CHANGE }.toString())
                StatisticRow("库存变化", changeRecords.count { it.changeType == ProductChangeType.STOCK_CHANGE }.toString())
                StatisticRow("可用性变化", changeRecords.count { it.changeType == ProductChangeType.AVAILABILITY_CHANGE }.toString())
                StatisticRow("标题变化", changeRecords.count { it.changeType == ProductChangeType.TITLE_CHANGE }.toString())
                StatisticRow("秒杀状态变化", changeRecords.count { it.changeType == ProductChangeType.SECKILL_STATUS_CHANGE }.toString())
            }
        }

        // 执行统计
        Card(
            colors = cardThemeOverlay(),
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "执行统计",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(bottom = 12.dp)
                )

                val totalExecutions = monitoringPlans.sumOf { it.executedCount }
                val avgExecutions = if (monitoringPlans.isNotEmpty()) totalExecutions / monitoringPlans.size else 0
                val maxExecutions = monitoringPlans.maxOfOrNull { it.executedCount } ?: 0

                StatisticRow("总执行次数", totalExecutions.toString())
                StatisticRow("平均执行次数", avgExecutions.toString())
                StatisticRow("最大执行次数", maxExecutions.toString())

                val recentlyExecuted = monitoringPlans.count { plan ->
                    plan.lastExecutedAt?.let { lastExecuted ->
                        System.currentTimeMillis() - lastExecuted.time < 24 * 60 * 60 * 1000 // 24小时内
                    } ?: false
                }
                StatisticRow("24小时内执行", recentlyExecuted.toString())
            }
        }
    }
}

/**
 * 统计行组件
 */
@Composable
fun StatisticRow(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            fontSize = 14.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.primary
        )
    }
}
