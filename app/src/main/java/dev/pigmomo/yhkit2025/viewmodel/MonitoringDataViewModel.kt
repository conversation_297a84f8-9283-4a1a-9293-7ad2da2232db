package dev.pigmomo.yhkit2025.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.State
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeRecordEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeType
import dev.pigmomo.yhkit2025.data.repository.productmonitor.ProductMonitorRepository
import dev.pigmomo.yhkit2025.service.productmonitor.MonitoringServiceManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch

/**
 * 监控数据ViewModel
 * 管理商品监控数据的筛选、展示和图表数据
 */
class MonitoringDataViewModel(
    private val productMonitorRepository: ProductMonitorRepository,
    application: Application
) : AndroidViewModel(application) {

    // 所有商品数据
    private val _allProducts = MutableStateFlow<List<ProductMonitorEntity>>(emptyList())
    val allProducts: StateFlow<List<ProductMonitorEntity>> = _allProducts.asStateFlow()

    // 筛选后的商品数据
    private val _filteredProducts = MutableStateFlow<List<ProductMonitorEntity>>(emptyList())
    val filteredProducts: StateFlow<List<ProductMonitorEntity>> = _filteredProducts.asStateFlow()

    // 所有变化记录
    private val _allChangeRecords = MutableStateFlow<List<ProductChangeRecordEntity>>(emptyList())
    val allChangeRecords: StateFlow<List<ProductChangeRecordEntity>> = _allChangeRecords.asStateFlow()

    // 筛选后的变化记录
    private val _filteredChangeRecords = MutableStateFlow<List<ProductChangeRecordEntity>>(emptyList())
    val filteredChangeRecords: StateFlow<List<ProductChangeRecordEntity>> = _filteredChangeRecords.asStateFlow()

    // 加载状态
    private val _isLoading = mutableStateOf(true)
    val isLoading: State<Boolean> = _isLoading

    // 筛选条件
    private val _productIdFilter = MutableStateFlow("")
    val productIdFilter: StateFlow<String> = _productIdFilter.asStateFlow()

    private val _sellerIdFilter = MutableStateFlow("")
    val sellerIdFilter: StateFlow<String> = _sellerIdFilter.asStateFlow()

    // 选中的商品
    private val _selectedProduct = mutableStateOf<ProductMonitorEntity?>(null)
    val selectedProduct: State<ProductMonitorEntity?> = _selectedProduct

    // 可用的sellerId列表
    private val _availableSellerIds = MutableStateFlow<List<String>>(emptyList())
    val availableSellerIds: StateFlow<List<String>> = _availableSellerIds.asStateFlow()

    // 图表数据
    private val _priceChartData = MutableStateFlow<List<Pair<Long, Float>>>(emptyList())
    val priceChartData: StateFlow<List<Pair<Long, Float>>> = _priceChartData.asStateFlow()

    private val _stockChartData = MutableStateFlow<List<Pair<Long, Int>>>(emptyList())
    val stockChartData: StateFlow<List<Pair<Long, Int>>> = _stockChartData.asStateFlow()

    init {
        loadData()
        setupFiltering()
    }

    /**
     * 加载数据
     */
    private fun loadData() {
        viewModelScope.launch {
            try {
                _isLoading.value = true

                // 监听商品数据变化
                productMonitorRepository.getAllProducts().collect { products ->
                    _allProducts.value = products
                    updateAvailableSellerIds(products)
                }
            } catch (e: Exception) {
                // 处理错误
            } finally {
                _isLoading.value = false
            }
        }

        viewModelScope.launch {
            try {
                // 监听变化记录数据
                productMonitorRepository.getAllChangeRecords().collect { records ->
                    _allChangeRecords.value = records.sortedByDescending { it.changeTime }
                }
            } catch (e: Exception) {
                // 处理错误
            }
        }
    }

    /**
     * 设置筛选逻辑
     */
    private fun setupFiltering() {
        viewModelScope.launch {
            combine(
                _allProducts,
                _productIdFilter,
                _sellerIdFilter
            ) { products, productIdFilter, sellerIdFilter ->
                Triple(products, productIdFilter, sellerIdFilter)
            }.collect { (products, productIdFilter, sellerIdFilter) ->
                applyFilters(products, productIdFilter, sellerIdFilter)
            }
        }

        viewModelScope.launch {
            combine(
                _allChangeRecords,
                _filteredProducts
            ) { records, filteredProducts ->
                Pair(records, filteredProducts)
            }.collect { (records, filteredProducts) ->
                filterChangeRecords(records, filteredProducts)
            }
        }
    }

    /**
     * 应用筛选条件
     */
    private fun applyFilters(
        products: List<ProductMonitorEntity>,
        productIdFilter: String,
        sellerIdFilter: String
    ) {
        val filtered = products.filter { product ->
            val matchesProductId = productIdFilter.isEmpty() || 
                product.id.contains(productIdFilter, ignoreCase = true)
            val matchesSellerId = sellerIdFilter.isEmpty() || 
                product.sellerId == sellerIdFilter

            matchesProductId && matchesSellerId
        }
        _filteredProducts.value = filtered
    }

    /**
     * 筛选变化记录
     */
    private fun filterChangeRecords(
        records: List<ProductChangeRecordEntity>,
        filteredProducts: List<ProductMonitorEntity>
    ) {
        val productIds = filteredProducts.map { it.id }.toSet()
        val filtered = records.filter { record ->
            productIds.contains(record.productId)
        }
        _filteredChangeRecords.value = filtered
    }

    /**
     * 更新可用的sellerId列表
     */
    private fun updateAvailableSellerIds(products: List<ProductMonitorEntity>) {
        val sellerIds = products.map { it.sellerId }
            .distinct()
            .filter { it.isNotEmpty() }
            .sorted()
        _availableSellerIds.value = sellerIds
    }

    /**
     * 设置商品ID筛选条件
     */
    fun setProductIdFilter(filter: String) {
        _productIdFilter.value = filter
    }

    /**
     * 设置sellerId筛选条件
     */
    fun setSellerIdFilter(filter: String) {
        _sellerIdFilter.value = filter
    }

    /**
     * 清除所有筛选条件
     */
    fun clearFilters() {
        _productIdFilter.value = ""
        _sellerIdFilter.value = ""
    }

    /**
     * 选择商品
     */
    fun selectProduct(product: ProductMonitorEntity?) {
        _selectedProduct.value = product
        if (product != null) {
            generateChartData(product.id)
        }
    }

    /**
     * 生成图表数据
     */
    private fun generateChartData(productId: String) {
        viewModelScope.launch {
            try {
                // 获取该商品的变化记录
                productMonitorRepository.getProductChangeRecords(productId).collect { records ->
                    val priceChanges = mutableListOf<Pair<Long, Float>>()
                    val stockChanges = mutableListOf<Pair<Long, Int>>()

                    records.forEach { record ->
                        when (record.changeType) {
                            ProductChangeType.PRICE_CHANGE -> {
                                try {
                                    val price = record.newValue.toFloatOrNull()
                                    if (price != null) {
                                        priceChanges.add(record.changeTime.time to price / 100f)
                                    }
                                } catch (e: Exception) {
                                    // 忽略解析错误
                                }
                            }
                            ProductChangeType.STOCK_CHANGE -> {
                                try {
                                    val stock = record.newValue.toIntOrNull()
                                    if (stock != null) {
                                        stockChanges.add(record.changeTime.time to stock)
                                    }
                                } catch (e: Exception) {
                                    // 忽略解析错误
                                }
                            }
                            else -> {
                                // 其他类型的变化暂不处理
                            }
                        }
                    }

                    _priceChartData.value = priceChanges.sortedBy { it.first }
                    _stockChartData.value = stockChanges.sortedBy { it.first }
                }
            } catch (e: Exception) {
                // 处理错误
            }
        }
    }

    /**
     * 刷新数据
     */
    fun refreshData() {
        loadData()
    }

    /**
     * ViewModel工厂
     */
    class Factory(
        private val productMonitorRepository: ProductMonitorRepository,
        private val application: Application
    ) : ViewModelProvider.Factory {
        @Suppress("UNCHECKED_CAST")
        override fun <T : androidx.lifecycle.ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(MonitoringDataViewModel::class.java)) {
                return MonitoringDataViewModel(productMonitorRepository, application) as T
            }
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }
}
