# 监控数据展示功能

## 概述

新增了一个专门用于展示监控商品数据的Screen，提供了全面的监控数据查看和分析功能。

## 功能特性

### 1. 监控概览
- 显示监控计划总数、启用计划数量
- 显示监控商品总数、可用商品数量
- 展示所有监控计划的基本信息和状态

### 2. 商品数据
- 展示所有被监控商品的详细信息
- 包括商品标题、副标题、价格、库存状态
- 显示商品ID、SKU码、最后更新时间
- 用颜色标识商品状态（有货/缺货/秒杀）

### 3. 变化记录
- 显示所有商品的历史变化记录
- 按变化类型分类（价格变化、库存变化、可用性变化等）
- 显示变化前后的值对比
- 按时间倒序排列，最新变化在前

### 4. 统计信息
- 监控计划统计（总数、启用/禁用、操作类型分布）
- 商品统计（总数、有货/缺货、秒杀商品数量）
- 变化记录统计（各类型变化的数量）
- 执行统计（总执行次数、平均执行次数、最近执行情况）

## 使用方法

### 1. 从监控任务弹窗进入
在订单管理页面中：
1. 点击"监控任务"按钮打开监控任务弹窗
2. 在弹窗中点击"监控数据"按钮
3. 系统会打开新的监控数据页面

### 2. 页面导航
- 使用顶部标签页切换不同的数据视图
- 点击左上角返回按钮回到上一页面
- 点击右上角刷新按钮重新加载数据

### 3. 数据查看
- **监控概览**：快速了解整体监控状况
- **商品数据**：查看具体商品的当前状态
- **变化记录**：追踪商品信息的历史变化
- **统计信息**：分析监控效果和趋势

## 技术实现

### 文件结构
```
app/src/main/java/dev/pigmomo/yhkit2025/
├── MonitoringDataActivity.kt                    # 监控数据Activity
├── ui/screens/MonitoringDataScreen.kt          # 监控数据Screen组件
├── ui/dialog/productmonitor/
│   └── MonitoringTaskDialog.kt                 # 修改了导航逻辑
└── ui/screens/OrderScreen.kt                   # 修改了Intent调用
```

### 数据源
- `MonitoringPlanRepository`：监控计划数据
- `ProductMonitorRepository`：商品监控数据和变化记录
- 实时监听数据变化，自动刷新显示

### UI组件
- 使用Material3设计规范
- 响应式布局，适配不同屏幕尺寸
- 卡片式设计，信息层次清晰
- 颜色编码，快速识别状态

## 扩展功能

### 可能的增强功能
1. **数据导出**：支持将监控数据导出为CSV或Excel文件
2. **图表展示**：添加价格趋势图、库存变化图等可视化图表
3. **筛选功能**：按商品类型、价格范围、时间范围筛选数据
4. **搜索功能**：支持按商品名称、ID等搜索
5. **通知设置**：配置重要变化的通知提醒

### 性能优化
1. **分页加载**：对于大量数据使用分页加载
2. **缓存机制**：缓存常用数据减少数据库查询
3. **懒加载**：按需加载详细数据

## 注意事项

1. **数据实时性**：数据会根据监控任务的执行自动更新
2. **存储空间**：变化记录会随时间累积，建议定期清理旧数据
3. **性能考虑**：大量监控商品可能影响页面加载速度
4. **权限要求**：需要相应的数据库访问权限

## 故障排除

### 常见问题
1. **数据不显示**：检查监控任务是否正常运行
2. **加载缓慢**：可能是数据量过大，考虑优化查询或分页
3. **统计不准确**：确保数据库数据完整性

### 调试方法
1. 查看应用日志了解数据加载情况
2. 检查数据库中的数据是否正确
3. 验证Repository层的数据查询逻辑
