# 监控数据界面 V2.0

## 概述

重新设计的监控数据界面，采用ViewModel架构，以商品ID和sellerId为筛选项，提供更好的数据展示和图表功能。

## 新功能特性

### 1. ViewModel架构
- 使用`MonitoringDataViewModel`管理状态和业务逻辑
- 响应式数据流，自动更新UI
- 更好的状态管理和生命周期处理

### 2. 高级筛选功能
- **商品ID筛选**：支持模糊搜索商品ID
- **卖家ID筛选**：下拉选择可用的卖家ID
- **筛选条件显示**：以Chip形式显示当前筛选条件
- **快速清除**：一键清除单个或全部筛选条件

### 3. 四个主要标签页

#### 商品列表
- 显示筛选后的商品列表
- 点击商品可查看详情
- 显示商品基本信息、价格、库存状态

#### 商品详情
- 详细的商品信息展示
- 包括基本信息、价格信息、库存信息、监控信息
- 结构化的信息展示

#### 变化记录
- 显示筛选商品的所有变化记录
- 按变化类型分类并用颜色标识
- 显示变化前后的值对比

#### 数据图表
- 价格趋势图表
- 库存变化图表
- 支持扩展更多图表类型

### 4. 用户体验优化
- 响应式设计，适配不同屏幕
- 加载状态指示
- 错误处理
- 流畅的导航体验

## 技术架构

### 文件结构
```
app/src/main/java/dev/pigmomo/yhkit2025/
├── viewmodel/
│   └── MonitoringDataViewModel.kt          # ViewModel层
├── ui/screens/
│   ├── MonitoringDataScreen.kt             # 主Screen组件
│   └── MonitoringDataComponents.kt         # UI组件库
├── MonitoringDataActivity.kt               # Activity容器
└── data/model/productmonitor/              # 数据模型
```

### 数据流
```
Repository -> ViewModel -> UI State -> Compose UI
     ↑                                        ↓
     ←-------- User Actions ←-----------------
```

### 状态管理
- `StateFlow`用于响应式数据流
- `mutableStateOf`用于UI状态
- 自动筛选和数据更新

## 使用方法

### 1. 启动界面
在订单管理页面点击"监控任务" -> "监控数据"按钮

### 2. 筛选数据
1. 点击顶部筛选按钮
2. 输入商品ID或选择卖家ID
3. 点击"应用"按钮
4. 查看筛选后的结果

### 3. 查看商品详情
1. 在商品列表中点击任意商品
2. 切换到"商品详情"标签页
3. 查看详细信息

### 4. 查看数据图表
1. 选择一个商品
2. 切换到"数据图表"标签页
3. 查看价格和库存趋势

## 扩展功能

### 图表库集成
当前使用简化的图表显示，可以集成专业图表库：
- **MPAndroidChart**：Android原生图表库
- **Compose Charts**：Jetpack Compose图表库
- **Vico**：现代化的Compose图表库

### 数据导出
可以添加数据导出功能：
- CSV格式导出
- Excel格式导出
- PDF报告生成

### 实时更新
可以添加实时数据更新：
- WebSocket连接
- 定时刷新
- 推送通知

## 性能优化

### 1. 数据分页
对于大量数据，可以实现分页加载：
```kotlin
// 在ViewModel中添加分页逻辑
private val _currentPage = MutableStateFlow(0)
private val pageSize = 20
```

### 2. 缓存机制
缓存常用数据减少数据库查询：
```kotlin
// 缓存筛选结果
private val filterCache = mutableMapOf<String, List<ProductMonitorEntity>>()
```

### 3. 懒加载
按需加载详细数据：
```kotlin
// 只在需要时加载图表数据
fun loadChartData(productId: String) {
    // 懒加载逻辑
}
```

## 注意事项

1. **内存管理**：大量数据时注意内存使用
2. **数据一致性**：确保筛选和显示的数据一致
3. **用户体验**：加载状态和错误处理
4. **性能监控**：监控界面响应时间

## 故障排除

### 常见问题
1. **数据不显示**：检查Repository连接和权限
2. **筛选不生效**：验证筛选逻辑和数据流
3. **图表显示异常**：检查数据格式和图表配置

### 调试方法
1. 查看ViewModel日志
2. 检查数据库数据
3. 验证筛选条件
4. 测试数据流
